Metadata-Version: 2.1
Name: hangul-romanize
Version: 0.1.0
Summary: Rominize Hangul strings.
Home-page: https://github.com/youknowone/hangul-romanize
Author: <PERSON><PERSON>
Author-email: <EMAIL>
License: UNKNOWN
Platform: UNKNOWN
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5

Hangul romanization tool
~~~~~~~~~~~~~~~~~~~~~~~~

.. image:: https://travis-ci.org/youknowone/hangul-romanize.svg?branch=master
    :target: https://travis-ci.org/youknowone/hangul-romanize

You can install the package from PyPI

    $ pip install hangul-romanize


Example
-------

Prelude::
    >>> from hangul_romanize import Transliter
    >>> from hangul_romanize.rule import academic
    >>>
    >>> transliter = Transliter(academic)
    >>> print(transliter.translit(u'안녕하세요'))
    annyeonghase-yo


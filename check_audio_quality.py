# check_audio_quality.py - 참조 음성 파일 품질 체크
import librosa
import numpy as np
import os

def analyze_audio_file(file_path):
    """음성 파일의 품질을 분석합니다"""
    
    if not os.path.exists(file_path):
        print(f"❌ 파일을 찾을 수 없습니다: {file_path}")
        return False
    
    try:
        # 오디오 파일 로드
        y, sr = librosa.load(file_path, sr=None)
        duration = len(y) / sr
        
        print(f"\n📊 '{file_path}' 분석 결과:")
        print(f"   📏 길이: {duration:.2f}초")
        print(f"   🎵 샘플링 레이트: {sr} Hz")
        print(f"   📈 최대 음량: {np.max(np.abs(y)):.3f}")
        print(f"   📉 평균 음량: {np.mean(np.abs(y)):.3f}")
        
        # 품질 평가
        quality_score = 0
        recommendations = []
        
        # 1. 길이 체크
        if 3 <= duration <= 30:
            print(f"   ✅ 길이: 적절함 ({duration:.1f}초)")
            quality_score += 25
        elif duration < 3:
            print(f"   ⚠️  길이: 너무 짧음 ({duration:.1f}초)")
            recommendations.append("3초 이상의 음성을 사용하세요")
        else:
            print(f"   ⚠️  길이: 너무 김 ({duration:.1f}초)")
            recommendations.append("30초 이하의 음성을 사용하세요")
        
        # 2. 샘플링 레이트 체크
        if sr >= 16000:
            print(f"   ✅ 샘플링 레이트: 좋음 ({sr} Hz)")
            quality_score += 25
        else:
            print(f"   ⚠️  샘플링 레이트: 낮음 ({sr} Hz)")
            recommendations.append("16kHz 이상의 음질을 권장합니다")
        
        # 3. 음량 체크
        max_volume = np.max(np.abs(y))
        if 0.1 <= max_volume <= 0.95:
            print(f"   ✅ 음량: 적절함")
            quality_score += 25
        elif max_volume < 0.1:
            print(f"   ⚠️  음량: 너무 작음")
            recommendations.append("음량을 높여주세요")
        else:
            print(f"   ⚠️  음량: 너무 큼 (클리핑 가능)")
            recommendations.append("음량을 낮춰주세요")
        
        # 4. 무음 구간 체크
        silence_threshold = 0.01
        silence_ratio = np.sum(np.abs(y) < silence_threshold) / len(y)
        if silence_ratio < 0.3:
            print(f"   ✅ 무음 구간: 적절함 ({silence_ratio*100:.1f}%)")
            quality_score += 25
        else:
            print(f"   ⚠️  무음 구간: 너무 많음 ({silence_ratio*100:.1f}%)")
            recommendations.append("무음 구간을 줄여주세요")
        
        # 전체 품질 점수
        print(f"\n🎯 전체 품질 점수: {quality_score}/100")
        
        if quality_score >= 80:
            print("   🌟 우수: XTTS-v2에 최적화된 품질입니다!")
        elif quality_score >= 60:
            print("   👍 양호: 사용 가능하지만 개선 여지가 있습니다")
        else:
            print("   ⚠️  개선 필요: 더 나은 품질의 음성을 권장합니다")
        
        # 개선 권장사항
        if recommendations:
            print(f"\n💡 개선 권장사항:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        
        return quality_score >= 60
        
    except Exception as e:
        print(f"❌ 분석 중 오류 발생: {e}")
        return False

def main():
    print("=== 참조 음성 파일 품질 체크 ===")
    
    # 체크할 파일들
    files_to_check = [
        "japanese_voice.wav",  # 일본어 음성
        "my_voice.wav",        # 기본 참조 음성
    ]
    
    print("📁 다음 파일들을 체크합니다:")
    for file in files_to_check:
        print(f"   - {file}")
    
    all_good = True
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            is_good = analyze_audio_file(file_path)
            if not is_good:
                all_good = False
        else:
            print(f"\n📁 '{file_path}' 파일이 없습니다 (선택사항)")
    
    print(f"\n{'='*50}")
    if all_good:
        print("🎉 모든 음성 파일이 좋은 품질입니다!")
        print("이제 japanese_to_korean.py를 실행해보세요.")
    else:
        print("⚠️  일부 파일의 품질 개선이 필요합니다.")
        print("위의 권장사항을 참고하여 음성을 다시 준비해주세요.")

if __name__ == "__main__":
    main()

# gpu_performance_test.py - GPU vs CPU 성능 비교 테스트
import torch
import time
from TTS.api import TTS
import os

print("=== GPU vs CPU 성능 비교 테스트 ===")

# 테스트용 텍스트
test_text = "안녕하세요. GPU와 CPU 성능을 비교하는 테스트입니다."

# 참조 음성 파일 확인
reference_voice_path = "my_voice.wav"
if not os.path.exists(reference_voice_path):
    print(f"⚠️  참조 음성 파일 '{reference_voice_path}'이 없습니다.")
    print("테스트를 위해 샘플 음성 파일을 준비해주세요.")
    exit()

def test_device_performance(device_name):
    """특정 디바이스에서 TTS 성능 테스트"""
    print(f"\n🔄 {device_name.upper()} 테스트 시작...")
    
    try:
        # 모델 로드
        start_time = time.time()
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(device_name)
        load_time = time.time() - start_time
        print(f"   모델 로드 시간: {load_time:.2f}초")
        
        # 음성 합성
        output_file = f"test_output_{device_name}.wav"
        start_time = time.time()
        tts.tts_to_file(
            text=test_text,
            speaker_wav=reference_voice_path,
            language="ko",
            file_path=output_file,
        )
        synthesis_time = time.time() - start_time
        print(f"   음성 합성 시간: {synthesis_time:.2f}초")
        print(f"   출력 파일: {output_file}")
        
        return load_time, synthesis_time
        
    except Exception as e:
        print(f"   ❌ 오류 발생: {e}")
        return None, None

# GPU 사용 가능 여부 확인
if torch.cuda.is_available():
    print(f"🚀 GPU: {torch.cuda.get_device_name(0)}")
    print(f"💾 GPU 메모리: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # GPU 테스트
    gpu_load_time, gpu_synthesis_time = test_device_performance("cuda")
    
    # CPU 테스트 (비교용)
    print(f"\n💻 CPU 테스트도 실행하시겠습니까? (y/n): ", end="")
    response = input().lower().strip()
    
    if response == 'y' or response == 'yes':
        cpu_load_time, cpu_synthesis_time = test_device_performance("cpu")
        
        # 성능 비교
        if gpu_load_time and cpu_load_time:
            print(f"\n📊 성능 비교 결과:")
            print(f"   모델 로드 속도: GPU가 CPU보다 {cpu_load_time/gpu_load_time:.1f}배 빠름")
            print(f"   음성 합성 속도: GPU가 CPU보다 {cpu_synthesis_time/gpu_synthesis_time:.1f}배 빠름")
    else:
        print("\n✅ GPU 테스트만 완료되었습니다.")
        
else:
    print("❌ CUDA GPU를 사용할 수 없습니다.")
    print("CPU로 테스트를 진행합니다...")
    test_device_performance("cpu")

print(f"\n🎉 성능 테스트 완료!")
print("이제 run_tts.py를 사용하여 실제 음성 복제를 시작할 수 있습니다.")

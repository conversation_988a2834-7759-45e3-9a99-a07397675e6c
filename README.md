# XTTS-v2 음성 복제 프로젝트

이 프로젝트는 Coqui TTS의 XTTS-v2 모델을 사용하여 음성을 복제하고 한국어 텍스트를 음성으로 변환하는 프로그램입니다.

## 설치 완료 상태

✅ Python 3.12.8 가상환경 설정 완료
✅ Coqui TTS 라이브러리 설치 완료
✅ XTTS-v2 모델 사용 준비 완료

## 사용 방법

### 1. 참조 음성 파일 준비
- 복제하고 싶은 목소리의 오디오 파일을 준비합니다
- 파일명을 `my_voice.wav`로 변경하거나, `run_tts.py`에서 파일명을 수정합니다
- 파일을 이 폴더(`tts_voiceclone`)에 복사합니다

**참조 음성 파일 권장사항:**
- 길이: 5초 이상
- 품질: 잡음이 없고 선명한 음질
- 형식: WAV, MP3 등 일반적인 오디오 형식

### 2. 텍스트 수정 (선택사항)
`run_tts.py` 파일을 열어서 다음 부분을 수정할 수 있습니다:

```python
# 생성할 한국어 텍스트
text_to_speak = "안녕하세요. 제 컴퓨터에서 XTTS-v2를 실행하고 있습니다."

# 저장할 파일 이름
output_filename = "korean_output.wav"
```

### 3. 프로그램 실행

1. 가상환경이 활성화되어 있는지 확인:
   ```
   .\venv\Scripts\activate
   ```

2. 프로그램 실행:
   ```
   python run_tts.py
   ```

### 4. 결과 확인
- 성공적으로 실행되면 `korean_output.wav` 파일이 생성됩니다
- 이 파일을 재생하여 복제된 음성을 확인할 수 있습니다

## 주요 기능

- **다국어 지원**: 한국어, 영어, 일본어, 중국어 등 다양한 언어 지원
- **실시간 음성 복제**: 참조 음성 파일만으로 즉시 음성 복제 가능
- **고품질 출력**: XTTS-v2 모델의 고품질 음성 합성

## 문제 해결

### GPU 사용 시 더 빠른 처리
- NVIDIA GPU가 있다면 CUDA를 설치하여 더 빠른 처리 가능
- CPU로도 동작하지만 시간이 더 오래 걸릴 수 있습니다

### 오류 발생 시
1. 참조 음성 파일이 올바른 위치에 있는지 확인
2. 파일 형식이 지원되는지 확인 (WAV, MP3 등)
3. 가상환경이 활성화되어 있는지 확인

## 지원 언어 코드

스크립트에서 `language` 매개변수를 변경하여 다른 언어로도 생성 가능:

- 한국어: "ko"
- 영어: "en"
- 일본어: "ja"
- 중국어: "zh"
- 스페인어: "es"
- 프랑스어: "fr"
- 독일어: "de"

## 라이선스

이 프로젝트는 Coqui TTS 라이브러리를 사용합니다. 상업적 사용 시 해당 라이선스를 확인하시기 바랍니다.

# quick_guide.py - 빠른 사용 가이드
import os

def print_guide():
    print("🎤 XTTS-v2 음성 복제 빠른 가이드")
    print("=" * 50)
    
    print("\n📋 1단계: 참조 음성 파일 준비")
    print("   • 길이: 3-10초 (5초 권장)")
    print("   • 품질: 깨끗하고 명확한 음질")
    print("   • 언어: 어떤 언어든 가능")
    
    print("\n🌍 2단계: 사용 목적에 따른 파일명")
    print("   • 일반 사용: my_voice.wav")
    print("   • 일본어→한국어: japanese_voice.wav")
    
    print("\n🔧 3단계: 실행할 스크립트 선택")
    
    # 파일 존재 여부 체크
    files_status = {
        "my_voice.wav": os.path.exists("my_voice.wav"),
        "japanese_voice.wav": os.path.exists("japanese_voice.wav")
    }
    
    print("\n📁 현재 파일 상태:")
    for filename, exists in files_status.items():
        status = "✅ 있음" if exists else "❌ 없음"
        print(f"   • {filename}: {status}")
    
    print("\n🚀 실행 가능한 스크립트:")
    
    if files_status["japanese_voice.wav"]:
        print("   1️⃣  일본어→한국어 변환:")
        print("      python japanese_to_korean.py")
        print("      (일본어 음성으로 한국어 텍스트 생성)")
    
    if files_status["my_voice.wav"]:
        print("   2️⃣  기본 음성 복제:")
        print("      python run_tts.py")
        print("      (같은 언어로 음성 복제)")
    
    print("   3️⃣  음성 품질 체크:")
    print("      python check_audio_quality.py")
    print("      (참조 음성 파일 품질 분석)")
    
    print("   4️⃣  GPU 성능 테스트:")
    print("      python gpu_performance_test.py")
    print("      (GPU vs CPU 성능 비교)")
    
    print("   5️⃣  모델 테스트:")
    print("      python test_model.py")
    print("      (설치 상태 확인)")
    
    print("\n💡 추천 순서:")
    if not any(files_status.values()):
        print("   1. 참조 음성 파일을 준비하세요")
        print("   2. python check_audio_quality.py (품질 체크)")
        print("   3. python japanese_to_korean.py 또는 python run_tts.py")
    elif files_status["japanese_voice.wav"]:
        print("   1. python check_audio_quality.py (품질 체크)")
        print("   2. python japanese_to_korean.py (일본어→한국어)")
    elif files_status["my_voice.wav"]:
        print("   1. python check_audio_quality.py (품질 체크)")
        print("   2. python run_tts.py (기본 음성 복제)")
    
    print("\n🎯 참조 음성 파일 준비 팁:")
    print("   • 조용한 환경에서 녹음")
    print("   • 일정한 속도와 톤으로 말하기")
    print("   • 배경 잡음 제거")
    print("   • 너무 크거나 작지 않은 적당한 음량")
    
    print("\n🚀 GPU 가속 활성화됨!")
    print("   • NVIDIA GeForce RTX 3060")
    print("   • CPU 대비 5-10배 빠른 처리")
    
    print("\n" + "=" * 50)
    print("🎉 준비가 되면 위의 스크립트를 실행해보세요!")

if __name__ == "__main__":
    print_guide()

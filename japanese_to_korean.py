# japanese_to_korean.py - 일본어 음성으로 한국어 말하기
import torch
from TTS.api import TTS
import os
import time

print("=== 일본어 음성 → 한국어 변환 ===")

# GPU 사용 가능 여부 확인
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"🚀 사용 디바이스: {device}")
if device == "cuda":
    print(f"   GPU: {torch.cuda.get_device_name(0)}")

# XTTS-v2 모델 로드
print("\n📥 XTTS-v2 모델 로드 중...")
try:
    tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(device)
    print("✅ 모델 로드 성공!")
except Exception as e:
    print(f"❌ 모델 로드 실패: {e}")
    exit()

# --- 설정 값 ---
# 일본어 참조 음성 파일 (3-10초 권장)
# 여러 확장자 지원 (wav, mp3 등)
possible_files = ["japanese_voice.wav", "japanese_voice.wav.mp3", "japanese_voice.mp3"]
reference_voice_path = None

# 존재하는 파일 찾기
for file_path in possible_files:
    if os.path.exists(file_path):
        reference_voice_path = file_path
        break

# 생성할 한국어 텍스트들 (여러 개 테스트 가능)
korean_texts = [
    "안녕하세요. 일본어 목소리로 한국어를 말하고 있습니다.",
    "오늘 날씨가 정말 좋네요. 어떻게 지내세요?",
    "XTTS-v2 모델은 정말 놀라운 기술입니다.",
    "다른 언어의 목소리로도 자연스럽게 말할 수 있어요.",
    "음성 복제 기술의 발전이 정말 빠르네요."
]

# 출력 파일 접두사
output_prefix = "korean_from_japanese"
# ---------------

# 참조 음성 파일 존재 여부 확인
if reference_voice_path is None:
    print(f"\n⚠️  참조 음성 파일을 찾을 수 없습니다.")
    print(f"\n📋 다음 파일명 중 하나를 사용하세요:")
    for file_name in possible_files:
        print(f"   • {file_name}")
    print("\n📋 참조 음성 파일 준비 방법:")
    print("   1. 일본어로 말하는 3-10초 길이의 음성 파일을 준비하세요")
    print("   2. 위의 파일명 중 하나로 변경하세요")
    print("   3. 이 스크립트와 같은 폴더에 복사하세요")
    print("\n💡 팁:")
    print("   - 깨끗하고 명확한 음질이 중요합니다")
    print("   - 배경 잡음이 없어야 합니다")
    print("   - 일정한 볼륨으로 녹음된 것이 좋습니다")
    print("   - WAV, MP3 등 다양한 형식 지원")
    exit()

print(f"\n🎤 참조 음성: {reference_voice_path}")
print(f"🎯 생성할 텍스트 수: {len(korean_texts)}개")
print(f"🌏 언어 변환: 일본어 음성 → 한국어 텍스트")

# 각 텍스트에 대해 음성 생성
for i, text in enumerate(korean_texts, 1):
    print(f"\n🔄 [{i}/{len(korean_texts)}] 음성 생성 중...")
    print(f"   텍스트: {text}")
    
    output_filename = f"{output_prefix}_{i:02d}.wav"
    
    try:
        start_time = time.time()
        tts.tts_to_file(
            text=text,
            speaker_wav=reference_voice_path,
            language="ko",  # 한국어로 출력
            file_path=output_filename,
        )
        generation_time = time.time() - start_time
        
        print(f"   ✅ 완료! ({generation_time:.2f}초)")
        print(f"   📁 저장: {output_filename}")
        
    except Exception as e:
        print(f"   ❌ 오류: {e}")

print(f"\n🎉 모든 음성 생성 완료!")
print(f"📂 생성된 파일들:")
for i in range(1, len(korean_texts) + 1):
    filename = f"{output_prefix}_{i:02d}.wav"
    if os.path.exists(filename):
        print(f"   ✅ {filename}")

print(f"\n💡 사용 팁:")
print("   - 생성된 파일들을 재생해서 품질을 확인해보세요")
print("   - 더 긴 텍스트나 다른 내용으로도 테스트해보세요")
print("   - 참조 음성의 품질이 결과에 큰 영향을 줍니다")

# test_model.py - XTTS-v2 모델 로드 테스트
import torch
from TTS.api import TTS

print("=== XTTS-v2 모델 테스트 ===")

# GPU 사용 가능 여부 확인
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"사용 중인 디바이스: {device}")

if device == "cuda":
    print(f"GPU 이름: {torch.cuda.get_device_name(0)}")
    print(f"GPU 메모리: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
else:
    print("CPU를 사용합니다. GPU가 있다면 CUDA 설치를 권장합니다.")

# XTTS-v2 모델 로드 테스트
print("\nXTTS-v2 모델 로드 중...")
try:
    tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(device)
    print("✅ 모델 로드 성공!")
    
    # 사용 가능한 언어 확인
    print(f"\n지원 언어: {tts.languages}")
    
    print("\n🎉 설치가 완료되었습니다!")
    print("이제 참조 음성 파일(my_voice.wav)을 준비하고 run_tts.py를 실행하세요.")
    
except Exception as e:
    print(f"❌ 모델 로드 실패: {e}")
    print("인터넷 연결을 확인하고 다시 시도해주세요.")
